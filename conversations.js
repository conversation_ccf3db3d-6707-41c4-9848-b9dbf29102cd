// Configuração das conversas disponíveis
const CONVERSATIONS_CONFIG = [
    {
        phone: "11968523450",
        phoneFormatted: "(11) 96852-3450",
        contact: "<PERSON><PERSON>",
        path: "11968523450 - <PERSON><PERSON>"
    },
    {
        phone: "11968523450", 
        phoneFormatted: "(11) 96852-3450",
        contact: "Milton Toledo",
        path: "11968523450 - Milton Toledo"
    },
    {
        phone: "35999325759",
        phoneFormatted: "(35) 99932-5759", 
        contact: "<PERSON><PERSON>",
        path: "35999325759 - Du<PERSON>"
    },
    {
        phone: "35999325759",
        phoneFormatted: "(35) 99932-5759",
        contact: "Milton Toledo", 
        path: "35999325759 - Milton Toledo"
    }
];

// Função para obter as conversas
function getConversations() {
    return CONVERSATIONS_CONFIG;
}

// Função para extrair datas de uma conversa usando XMLHttpRequest (funciona com file://)
async function getConversationDates(conversationPath) {
    return new Promise((resolve) => {
        try {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', `${conversationPath}/conversation-data.js`, true);

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200 || xhr.status === 0) { // status 0 para file://
                        try {
                            const fileContent = xhr.responseText;

                            // Extrai as datas usando regex diretamente do texto
                            const dateMatches = fileContent.match(/(\d{2}\/\d{2}\/\d{4})/g);

                            if (dateMatches && dateMatches.length > 0) {
                                const firstDate = dateMatches[0];
                                const lastDate = dateMatches[dateMatches.length - 1];

                                resolve({
                                    startDate: firstDate,
                                    endDate: lastDate
                                });
                            } else {
                                resolve({ startDate: null, endDate: null });
                            }
                        } catch (error) {
                            console.error('Erro ao processar dados da conversa:', error);
                            resolve({ startDate: null, endDate: null });
                        }
                    } else {
                        console.error('Erro ao carregar arquivo:', xhr.status);
                        resolve({ startDate: null, endDate: null });
                    }
                }
            };

            xhr.onerror = function() {
                console.error('Erro na requisição XMLHttpRequest');
                resolve({ startDate: null, endDate: null });
            };

            xhr.send();

        } catch (error) {
            console.error('Erro ao carregar dados da conversa:', error);
            resolve({ startDate: null, endDate: null });
        }
    });
}
