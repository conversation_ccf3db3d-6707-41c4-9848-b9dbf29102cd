// Configuração das conversas disponíveis
const CONVERSATIONS_CONFIG = [
    {
        phone: "11968523450",
        phoneFormatted: "(11) 96852-3450",
        contact: "<PERSON><PERSON>",
        path: "11968523450 - <PERSON><PERSON>"
    },
    {
        phone: "11968523450", 
        phoneFormatted: "(11) 96852-3450",
        contact: "Milton Toledo",
        path: "11968523450 - Milton Toledo"
    },
    {
        phone: "35999325759",
        phoneFormatted: "(35) 99932-5759", 
        contact: "<PERSON><PERSON>",
        path: "35999325759 - Dudu"
    },
    {
        phone: "35999325759",
        phoneFormatted: "(35) 99932-5759",
        contact: "Milton Toledo", 
        path: "35999325759 - Milton Toledo"
    }
];

// Função para obter as conversas
function getConversations() {
    return CONVERSATIONS_CONFIG;
}

// Função para extrair datas de uma conversa carregando o script dinamicamente (resolve CORS)
async function getConversationDates(conversationPath) {
    return new Promise((resolve) => {
        try {
            // Cria um ID único para evitar conflitos
            const scriptId = `conversation-script-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            const functionName = `loadConversationData_${scriptId.replace(/[^a-zA-Z0-9]/g, '_')}`;

            // Remove qualquer script anterior com o mesmo ID (precaução)
            const existingScript = document.getElementById(scriptId);
            if (existingScript) {
                existingScript.remove();
            }

            // Cria o elemento script
            const script = document.createElement('script');
            script.id = scriptId;
            script.src = `${conversationPath}/conversation-data.js`;

            // Aguarda o carregamento do script
            script.onload = () => {
                try {
                    // Aguarda um pouco para garantir que a função foi definida
                    setTimeout(() => {
                        // Captura os dados imediatamente após o carregamento
                        let conversationText = '';

                        if (typeof loadEmbeddedConversationData === 'function') {
                            conversationText = loadEmbeddedConversationData();
                        } else if (typeof window.CONVERSATION_DATA === 'string') {
                            conversationText = window.CONVERSATION_DATA;
                        }

                        if (conversationText) {
                            // Extrai as datas usando regex diretamente do texto
                            const dateMatches = conversationText.match(/(\d{2}\/\d{2}\/\d{4})/g);

                            if (dateMatches && dateMatches.length > 0) {
                                const firstDate = dateMatches[0];
                                const lastDate = dateMatches[dateMatches.length - 1];

                                console.log(`Conversa ${conversationPath}: ${firstDate} - ${lastDate}`);

                                resolve({
                                    startDate: firstDate,
                                    endDate: lastDate
                                });
                            } else {
                                console.log(`Conversa ${conversationPath}: Nenhuma data encontrada`);
                                resolve({ startDate: null, endDate: null });
                            }
                        } else {
                            console.error(`Conversa ${conversationPath}: Dados não encontrados`);
                            resolve({ startDate: null, endDate: null });
                        }

                        // Remove o script após o uso para evitar conflitos
                        script.remove();

                        // Limpa variáveis globais para evitar conflitos
                        if (typeof window.CONVERSATION_DATA !== 'undefined') {
                            delete window.CONVERSATION_DATA;
                        }
                        if (typeof window.loadEmbeddedConversationData !== 'undefined') {
                            delete window.loadEmbeddedConversationData;
                        }
                    }, 100);
                } catch (error) {
                    console.error('Erro ao processar dados da conversa:', error);
                    script.remove();
                    resolve({ startDate: null, endDate: null });
                }
            };

            script.onerror = (error) => {
                console.error('Erro ao carregar script:', error);
                script.remove();
                resolve({ startDate: null, endDate: null });
            };

            // Adiciona o script ao head
            document.head.appendChild(script);

        } catch (error) {
            console.error('Erro ao carregar dados da conversa:', error);
            resolve({ startDate: null, endDate: null });
        }
    });
}
