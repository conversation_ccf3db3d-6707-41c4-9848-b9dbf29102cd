// Configuração das conversas disponíveis
const CONVERSATIONS_CONFIG = [
    {
        phone: "11968523450",
        phoneFormatted: "(11) 96852-3450",
        contact: "<PERSON><PERSON>",
        path: "11968523450 - <PERSON><PERSON>"
    },
    {
        phone: "11968523450", 
        phoneFormatted: "(11) 96852-3450",
        contact: "Milton Toledo",
        path: "11968523450 - Milton Toledo"
    },
    {
        phone: "35999325759",
        phoneFormatted: "(35) 99932-5759", 
        contact: "<PERSON><PERSON>",
        path: "35999325759 - Dudu"
    },
    {
        phone: "35999325759",
        phoneFormatted: "(35) 99932-5759",
        contact: "Milton Toledo", 
        path: "35999325759 - Milton Toledo"
    }
];

// Função para obter as conversas
function getConversations() {
    return CONVERSATIONS_CONFIG;
}

// Função para extrair datas de uma conversa usando fetch (resolve CORS e conflitos)
async function getConversationDates(conversationPath) {
    return new Promise(async (resolve) => {
        try {
            console.log(`=== Iniciando carregamento de ${conversationPath} ===`);

            // Tenta carregar o arquivo como texto usando fetch
            const response = await fetch(`${conversationPath}/conversation-data.js`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const fileContent = await response.text();
            console.log(`Conversa ${conversationPath}: Arquivo carregado, tamanho: ${fileContent.length} caracteres`);

            // Extrai o conteúdo da variável CONVERSATION_DATA usando regex
            const conversationDataMatch = fileContent.match(/const\s+CONVERSATION_DATA\s*=\s*`([\s\S]*?)`;/);

            if (!conversationDataMatch) {
                console.error(`Conversa ${conversationPath}: Não foi possível encontrar CONVERSATION_DATA no arquivo`);
                resolve({ startDate: null, endDate: null });
                return;
            }

            const conversationText = conversationDataMatch[1];
            console.log(`Conversa ${conversationPath}: Texto extraído, tamanho: ${conversationText.length} caracteres`);
            console.log(`Conversa ${conversationPath}: Primeiros 200 caracteres:`, conversationText.substring(0, 200));

            // Extrai as datas usando regex diretamente do texto
            const dateMatches = conversationText.match(/(\d{2}\/\d{2}\/\d{4})/g);

            if (dateMatches && dateMatches.length > 0) {
                const firstDate = dateMatches[0];
                const lastDate = dateMatches[dateMatches.length - 1];

                console.log(`Conversa ${conversationPath}: ${dateMatches.length} datas encontradas`);
                console.log(`Conversa ${conversationPath}: Primeira data: ${firstDate}, Última data: ${lastDate}`);
                console.log(`Conversa ${conversationPath}: Primeiras 5 datas:`, dateMatches.slice(0, 5));
                console.log(`Conversa ${conversationPath}: Últimas 5 datas:`, dateMatches.slice(-5));

                resolve({
                    startDate: firstDate,
                    endDate: lastDate
                });
            } else {
                console.log(`Conversa ${conversationPath}: Nenhuma data encontrada no texto`);
                resolve({ startDate: null, endDate: null });
            }

        } catch (error) {
            console.error(`Erro ao carregar dados da conversa ${conversationPath}:`, error);

            // Fallback: tenta usar o método antigo com script dinâmico
            console.log(`Tentando fallback com script dinâmico para ${conversationPath}`);

            try {
                const scriptId = `conversation-script-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                const script = document.createElement('script');
                script.id = scriptId;
                script.src = `${conversationPath}/conversation-data.js`;

                script.onload = () => {
                    setTimeout(() => {
                        let conversationText = '';

                        if (typeof loadEmbeddedConversationData === 'function') {
                            conversationText = loadEmbeddedConversationData();
                        } else if (typeof window.CONVERSATION_DATA === 'string') {
                            conversationText = window.CONVERSATION_DATA;
                        }

                        if (conversationText) {
                            const dateMatches = conversationText.match(/(\d{2}\/\d{2}\/\d{4})/g);
                            if (dateMatches && dateMatches.length > 0) {
                                resolve({
                                    startDate: dateMatches[0],
                                    endDate: dateMatches[dateMatches.length - 1]
                                });
                            } else {
                                resolve({ startDate: null, endDate: null });
                            }
                        } else {
                            resolve({ startDate: null, endDate: null });
                        }

                        script.remove();
                        if (typeof window.CONVERSATION_DATA !== 'undefined') {
                            delete window.CONVERSATION_DATA;
                        }
                        if (typeof window.loadEmbeddedConversationData !== 'undefined') {
                            delete window.loadEmbeddedConversationData;
                        }
                    }, 100);
                };

                script.onerror = () => {
                    script.remove();
                    resolve({ startDate: null, endDate: null });
                };

                document.head.appendChild(script);

            } catch (fallbackError) {
                console.error(`Fallback também falhou para ${conversationPath}:`, fallbackError);
                resolve({ startDate: null, endDate: null });
            }
        }
    });
}
