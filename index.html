<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Visualizer - Seleção de Conversas</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .phones-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 20px;
        }

        .phone-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .phone-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }

        .phone-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .phone-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #25D366, #128C7E);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
        }

        .phone-icon i {
            color: white;
            font-size: 24px;
        }

        .phone-info h2 {
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .phone-number {
            color: #666;
            font-size: 1rem;
            font-weight: 400;
        }

        .phone-description {
            color: #888;
            font-size: 0.9rem;
            font-style: italic;
            margin-top: 3px;
        }

        .conversations-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .conversation-item {
            display: flex;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .conversation-item:hover {
            background: #e3f2fd;
            border-color: #2196F3;
            transform: translateX(5px);
        }

        .conversation-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            box-shadow: 0 3px 10px rgba(33, 150, 243, 0.3);
            position: relative;
            overflow: hidden;
        }

        .profile-pic {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .profile-icon {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #2196F3, #1976D2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .conversation-info {
            flex: 1;
        }

        .conversation-name {
            font-size: 1.2rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .conversation-path {
            font-size: 0.9rem;
            color: #666;
            opacity: 0.8;
        }

        .conversation-arrow {
            color: #2196F3;
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        .conversation-item:hover .conversation-arrow {
            transform: translateX(5px);
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            margin-top: 50px;
        }

        .loading i {
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #ffebee;
            color: #c62828;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .phones-container {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .phone-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fab fa-whatsapp"></i> WhatsApp Visualizer</h1>
            <p>Selecione uma conversa para visualizar</p>
        </div>

        <div id="phonesContainer" class="phones-container">
            <div class="loading">
                <i class="fas fa-spinner"></i>
                Carregando conversas...
            </div>
        </div>
    </div>

    <script src="conversations.js"></script>
    <script>
        class ConversationSelector {
            constructor() {
                this.phones = [];
                this.init();
            }

            async init() {
                await this.loadConversations();
                await this.renderPhones();
            }

            async loadConversations() {
                try {
                    // Carrega a configuração dos telefones
                    if (typeof getPhonesConfig === 'function') {
                        this.phones = getPhonesConfig();
                        console.log('Telefones carregados:', this.phones);
                    } else {
                        throw new Error('Arquivo de configuração não encontrado');
                    }

                    if (this.phones.length === 0) {
                        throw new Error('Nenhum telefone configurado');
                    }
                } catch (error) {
                    console.error('Erro ao carregar telefones:', error);
                    this.showError('Erro ao carregar os telefones. Verifique se o arquivo conversations.js existe.');
                }
            }

            async renderPhones() {
                const container = document.getElementById('phonesContainer');

                if (this.phones.length === 0) {
                    return;
                }

                let html = '';
                for (const phoneConfig of this.phones) {
                    html += `
                        <div class="phone-card">
                            <div class="phone-header">
                                <div class="phone-icon">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="phone-info">
                                    <h2>${phoneConfig.title}</h2>
                                    <div class="phone-number">+55 ${phoneConfig.phoneFormatted}</div>
                                    ${phoneConfig.description ? `<div class="phone-description">${phoneConfig.description}</div>` : ''}
                                </div>
                            </div>
                            <div class="conversations-list">
                                ${phoneConfig.conversations.map(conv => {
                                    const dateRange = conv.startDate && conv.endDate
                                        ? (conv.startDate === conv.endDate
                                            ? conv.startDate
                                            : `${conv.startDate} - ${conv.endDate}`)
                                        : 'Datas não encontradas';

                                    return `
                                        <div class="conversation-item" onclick="openConversation('${conv.path}', '${conv.contact}')">
                                            <div class="conversation-avatar">
                                                <img src="${conv.path}/profile.jpg"
                                                     alt="${conv.contact}"
                                                     class="profile-pic"
                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                <div class="profile-icon" style="display: none;">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            </div>
                                            <div class="conversation-info">
                                                <div class="conversation-name">${conv.contact}</div>
                                                <div class="conversation-path">${dateRange}</div>
                                            </div>
                                            <div class="conversation-arrow">
                                                <i class="fas fa-chevron-right"></i>
                                            </div>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    `;
                }

                container.innerHTML = html;
            }

            formatPhoneNumber(phone) {
                // Formata o número de telefone brasileiro
                if (phone.length === 11) {
                    return `(${phone.substring(0, 2)}) ${phone.substring(2, 7)}-${phone.substring(7)}`;
                } else if (phone.length === 10) {
                    return `(${phone.substring(0, 2)}) ${phone.substring(2, 6)}-${phone.substring(6)}`;
                }
                return phone;
            }

            showError(message) {
                const container = document.getElementById('phonesContainer');
                container.innerHTML = `
                    <div class="error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>${message}</p>
                    </div>
                `;
            }
        }

        function openConversation(conversationPath, contactName) {
            // Redireciona para o visualizer com parâmetros
            const params = new URLSearchParams({
                conversation: conversationPath,
                contact: contactName
            });
            window.location.href = `whatsapp-visualizer.html?${params.toString()}`;
        }

        // Inicializa o seletor de conversas
        new ConversationSelector();
    </script>
</body>
</html>
