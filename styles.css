* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: #0a0a0a;
    height: 100vh;
    overflow: hidden;
}

.whatsapp-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 800px;
    margin: 0 auto;
    background: #111b21;
    position: relative;
}

/* Header */
.chat-header {
    background: #202c33;
    padding: 10px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #2a3942;
    z-index: 100;
}

.contact-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.back-button {
    background: none;
    border: none;
    color: #8696a0;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.back-button:hover {
    color: #e9edef;
    background: rgba(255, 255, 255, 0.1);
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #00a884;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.contact-details h3 {
    color: #e9edef;
    font-size: 16px;
    font-weight: 400;
}

.contact-details .status {
    color: #8696a0;
    font-size: 13px;
}

.header-actions {
    display: flex;
    gap: 20px;
    color: #8696a0;
    font-size: 18px;
}

.header-actions i {
    cursor: pointer;
    transition: color 0.2s;
}

.header-actions i:hover {
    color: #e9edef;
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><defs><pattern id="pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23pattern)"/></svg>');
    background-color: #0b141a;
    position: relative;
}

/* Sticky Date */
.sticky-date {
    position: sticky;
    top: 0;
    background: rgba(11, 20, 26, 0.9);
    backdrop-filter: blur(10px);
    text-align: center;
    padding: 8px 16px;
    margin: 0 auto 16px;
    border-radius: 8px;
    color: #8696a0;
    font-size: 13px;
    font-weight: 400;
    z-index: 50;
    width: fit-content;
    display: none;
}

/* Date Separator */
.date-separator {
    text-align: center;
    margin: 20px 0;
}

.date-badge {
    background: rgba(11, 20, 26, 0.8);
    color: #8696a0;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 13px;
    display: inline-block;
}

/* Message Bubble */
.message {
    margin-bottom: 8px;
    display: flex;
    align-items: flex-end;
}

.message.sent {
    justify-content: flex-end;
}

.message.received {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 65%;
    padding: 8px 12px;
    border-radius: 8px;
    position: relative;
    word-wrap: break-word;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.message.sent .message-bubble {
    background: #005c4b;
    color: #e9edef;
    border-bottom-right-radius: 2px;
}

.message.received .message-bubble {
    background: #202c33;
    color: #e9edef;
    border-bottom-left-radius: 2px;
}

.message-content {
    margin-bottom: 4px;
    line-height: 1.4;
    font-size: 14px;
}

.message-time {
    font-size: 11px;
    color: #8696a0;
    text-align: right;
    margin-top: 4px;
}

.message.received .message-time {
    text-align: left;
}

/* Media Messages */
.media-message {
    padding: 4px;
    border-radius: 8px;
    overflow: hidden;
}

.media-message img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 4px;
    cursor: pointer;
    transition: opacity 0.2s;
}

.media-message img:hover {
    opacity: 0.9;
}

.video-thumbnail {
    position: relative;
    cursor: pointer;
}

.video-thumbnail::after {
    content: '\f04b';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 48px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Audio Messages */
.audio-message {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    min-width: 200px;
}

.audio-play-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #00a884;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.audio-play-btn:hover {
    background: #008f72;
}

.audio-info {
    flex: 1;
}

.audio-duration {
    color: #8696a0;
    font-size: 13px;
}

.audio-progress {
    width: 100%;
    height: 4px;
    background: #3b4a54;
    border-radius: 2px;
    margin: 4px 0;
    overflow: hidden;
    cursor: pointer;
}

.audio-progress-bar {
    height: 100%;
    background: #00a884;
    width: 0%;
    transition: width 0.1s;
}

/* Contact Messages */
.contact-message {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(0, 168, 132, 0.1);
    border-left: 4px solid #00a884;
    border-radius: 4px;
}

.contact-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #00a884;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.contact-info-msg {
    flex: 1;
}

.contact-name {
    font-weight: 500;
    color: #e9edef;
    margin-bottom: 2px;
}

.contact-label {
    color: #8696a0;
    font-size: 13px;
}

/* Document Messages */
.document-message {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(33, 150, 243, 0.1);
    border-left: 4px solid #2196F3;
    border-radius: 4px;
    max-width: 300px;
}

.document-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: #2196F3;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    flex-shrink: 0;
}

.document-info {
    flex: 1;
    min-width: 0;
}

.document-name {
    font-weight: 500;
    color: #e9edef;
    margin-bottom: 2px;
    word-break: break-all;
    font-size: 14px;
}

.document-type {
    color: #8696a0;
    font-size: 12px;
}

.document-download {
    background: none;
    border: none;
    color: #2196F3;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s;
    flex-shrink: 0;
}

.document-download:hover {
    background: rgba(33, 150, 243, 0.2);
    color: #1976D2;
}

/* Link Messages */
.message-content a {
    color: #53bdeb;
    text-decoration: none;
}

.message-content a:hover {
    text-decoration: underline;
}

/* Chat Input */
.chat-input {
    background: #202c33;
    padding: 12px 16px;
    border-top: 1px solid #2a3942;
}

.input-container {
    display: flex;
    align-items: center;
    gap: 12px;
    background: #2a3942;
    border-radius: 24px;
    padding: 8px 16px;
}

.input-container i {
    color: #8696a0;
    font-size: 20px;
    cursor: pointer;
}

.input-container input {
    flex: 1;
    background: none;
    border: none;
    outline: none;
    color: #e9edef;
    font-size: 15px;
}

.input-container input::placeholder {
    color: #8696a0;
}

/* Modals */
.image-modal, .video-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
}

.modal-content {
    position: relative;
    margin: auto;
    padding: 20px;
    width: 90%;
    height: 90%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
}

.close-modal:hover {
    color: #bbb;
}

#modalImage {
    max-width: 100%;
    max-height: 80%;
    object-fit: contain;
    transition: transform 0.3s;
}

#modalVideo {
    max-width: 100%;
    max-height: 80%;
}

.image-controls {
    margin-top: 20px;
    display: flex;
    gap: 15px;
}

.image-controls button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

.image-controls button:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Scrollbar */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #202c33;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #3b4a54;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #4a5a64;
}

/* System Messages */
.system-message {
    text-align: center;
    margin: 16px auto;
    padding: 8px 16px;
    background: rgba(11, 20, 26, 0.8);
    color: #8696a0;
    font-size: 12px;
    font-style: italic;
    border-radius: 8px;
    max-width: 80%;
    line-height: 1.4;
}

/* Deleted Messages */
.deleted-message {
    font-style: italic;
    color: #8696a0;
}

/* Hidden Media */
.hidden-media {
    color: #8696a0;
    font-style: italic;
    display: flex;
    align-items: center;
    gap: 8px;
}

.hidden-media i {
    font-size: 16px;
}

/* Search Panel */
.search-panel {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: #2a2f32;
    border-bottom: 1px solid #3e4042;
    z-index: 1000;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.search-panel.active {
    transform: translateY(0);
}

.search-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    gap: 10px;
}

.search-header input {
    flex: 1;
    background: #3e4042;
    border: none;
    border-radius: 20px;
    padding: 10px 15px;
    color: #e9edef;
    font-size: 14px;
}

.search-header input:focus {
    outline: none;
    background: #4a4f52;
}

.search-header input::placeholder {
    color: #8696a0;
}

.search-close {
    background: none;
    border: none;
    color: #8696a0;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.search-close:hover {
    background: #3e4042;
}

.search-results {
    padding: 0 20px 15px;
}

.search-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.search-nav-btn {
    background: #3e4042;
    border: none;
    color: #8696a0;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.search-nav-btn:hover:not(:disabled) {
    background: #4a4f52;
    color: #e9edef;
}

.search-nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.search-counter {
    color: #8696a0;
    font-size: 14px;
    min-width: 80px;
    text-align: center;
}

/* Search Highlight */
.search-highlight {
    background: #ffd60a !important;
    color: #000 !important;
    padding: 2px 4px;
    border-radius: 3px;
}

.search-highlight.current {
    background: #ff6b35 !important;
    color: #fff !important;
}

/* Scroll to Bottom FAB */
.scroll-to-bottom-fab {
    position: absolute;
    bottom: 80px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: #242626;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    color: #8696a0;
    font-size: 18px;
    transition: all 0.3s ease;
    z-index: 100;
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
}

.scroll-to-bottom-fab.hidden {
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
}

.scroll-to-bottom-fab:hover {
    background: #3e4042;
    color: #e9edef;
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .whatsapp-container {
        height: 100vh;
        border-radius: 0;
    }

    .header-actions i {
        margin-left: 15px;
    }

    .message-bubble {
        max-width: 85%;
    }

    .search-header {
        padding: 10px 15px;
    }

    .search-results {
        padding: 0 15px 10px;
    }
}
